import React, { useState, useCallback } from 'react';
import { Input, Modal, Transfer, ConfigProvider, Table, Button } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import { envRequest } from '@/request';
import styles from './index.module.less';

/**
 * 溯源对象数据结构
 */
interface TraceObject {
  key: string; // 唯一标识
  title: string; // 显示名称
  description: string; // 描述信息
  company: string; // 公司名称
  traceIndex: string; // 溯源指标
}

/**
 * 溯源对象选择器组件属性
 */
interface TraceObjectSelectorProps {
  value?: string[]; // 已选择的溯源对象ID数组
  onChange?: (value: string[]) => void; // 选择变化时的回调函数
  disabled?: boolean; // 是否禁用
  isTraceEnabled?: boolean; // 是否开启溯源功能
  type?: 'view' | 'edit' | 'add'; // 组件状态类型
}

/**
 * 溯源对象选择器组件
 * 用于环境质量监测中的溯源对象选择功能
 * 显示为输入框样式，点击打开穿梭框进行选择
 */
const TraceObjectSelector: React.FC<TraceObjectSelectorProps> = ({
  value = [], // 已选择的溯源对象ID数组，默认为空数组
  onChange, // 选择变化时的回调函数
  disabled = false, // 是否禁用，默认为false
  isTraceEnabled = false, // 是否开启溯源功能，默认为false
  type = 'edit', // 组件状态类型，默认为编辑模式
}) => {
  // 穿梭框显示状态
  const [transferVisible, setTransferVisible] = useState<boolean>(false);
  // 穿梭框中已选择的keys，初始化为传入的value
  const [transferTargetKeys, setTransferTargetKeys] = useState<string[]>(value || []);
  // 溯源对象数据
  const [traceData, setTraceData] = useState<TraceObject[]>([]);
  // 数据加载状态
  const [loading, setLoading] = useState<boolean>(false);

  /**
   * 获取溯源对象数据
   * 每次打开模态框时都会调用，确保数据是最新的
   */
  const fetchTraceData = async () => {
    try {
      setLoading(true);
      // 调用接口获取污染源企业数据
      const response = await envRequest('/pollutantsDirectory/page', {
        method: 'POST',
        data: {
          currentPage: 1,
          pageSize: 1000, // 一次性获取所有数据
          condition: {
            // 可以添加查询条件，比如企业类型等
          },
        },
      });

      if (response && response.data) {
        // 将接口返回的数据转换为穿梭框需要的格式
        const formattedData: TraceObject[] = response.data.map(
          (item: Record<string, string | number>, index: number) => {
            const company = item.enterpriseName || '-';
            const traceIndex = item.chemicalName || '-';

            return {
              key: item.id || `trace_${index}`,
              title: `${company} - ${traceIndex}`, // 用于搜索和显示
              company,
              traceIndex,
            };
          },
        );

        // 直接设置数据
        setTraceData(formattedData);
      } else {
        setTraceData([]);
      }
    } catch {
      setTraceData([]);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 表格列配置
   */
  const tableColumns = [
    {
      dataIndex: 'company',
      title: '公司名称',
      width: '60%',
      ellipsis: true,
    },
    {
      dataIndex: 'traceIndex',
      title: '溯源指标',
      width: '40%',
      ellipsis: true,
    },
  ];

  /**
   * 自定义渲染函数 - 使用 Table 组件
   */
  const renderTransferList = useCallback(
    (props: any) => {
      const { filteredItems, onItemSelectAll, onItemSelect, selectedKeys } = props;

      // 确保 filteredItems 是数组且有数据
      const safeFilteredItems = Array.isArray(filteredItems) ? filteredItems : [];

      const rowSelection = {
        selectedRowKeys: selectedKeys || [],
        onSelectAll: (selected: boolean, _selectedRows: any[], changeRows: any[]) => {
          const treeSelectedKeys = changeRows.map(({ key }) => key);
          onItemSelectAll(treeSelectedKeys, selected);
        },
        onSelect: ({ key }: any, selected: boolean) => {
          onItemSelect(key, selected);
        },
      };

      // 处理行点击事件
      const handleRowClick = (record: any) => {
        const isSelected = (selectedKeys || []).includes(record.key);
        onItemSelect(record.key, !isSelected);
      };

      // 如果没有数据，显示空状态
      if (safeFilteredItems.length === 0) {
        return (
          <div className={styles.tableContainer}>
            <div
              style={{
                height: '320px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#999',
              }}
            >
              暂无数据
            </div>
          </div>
        );
      }

      return (
        <div className={styles.tableContainer}>
          <Table
            rowSelection={rowSelection}
            columns={tableColumns}
            dataSource={safeFilteredItems}
            size="small"
            pagination={false} // 禁用 Table 自带分页，使用 Transfer 的分页
            scroll={{
              y: 320, // 减少高度，为表头留出空间
              scrollToFirstRowOnChange: true, // 分页时滚动到顶部
            }}
            className={styles.table}
            showHeader
            bordered={false}
            rowKey="key" // 指定行的唯一标识
            onRow={(record) => ({
              onClick: () => handleRowClick(record), // 点击行选择
              className: styles.clickableRow,
            })}
            rowClassName={(record) =>
              (selectedKeys || []).includes(record.key) ? 'selected-row' : ''
            }
            loading={loading} // 使用loading状态显示加载状态
          />
        </div>
      );
    },
    [tableColumns],
  );
  /**
   * 打开穿梭框
   * 每次打开时都获取最新数据
   */
  const openModal = useCallback(async () => {
    // 如果完全禁用或未开启溯源，则不打开
    // disabled 用于完全禁用组件，type === 'view' 只是查看模式，仍可打开查看
    if (disabled || !isTraceEnabled) return;

    // 每次打开模态框时都获取最新数据
    await fetchTraceData();

    setTransferTargetKeys(value || []);
    setTransferVisible(true);
  }, [disabled, isTraceEnabled, value]);

  /**
   * 穿梭框确认操作
   */
  const handleTransferOk = useCallback(() => {
    if (onChange) {
      onChange(transferTargetKeys);
    }
    setTransferVisible(false);
  }, [onChange, transferTargetKeys]);

  /**
   * 穿梭框取消操作
   */
  const handleTransferCancel = useCallback(() => {
    setTransferTargetKeys(value || []);
    setTransferVisible(false);
  }, [value]);

  /**
   * 穿梭框数据变化处理
   */
  const handleTransferChange = useCallback((targetKeys: string[]) => {
    setTransferTargetKeys(targetKeys);
  }, []);

  // 如果未开启溯源，显示 "-"
  if (!isTraceEnabled) {
    return (
      <Input
        value="-"
        readOnly
        disabled
        style={{
          cursor: 'default',
          backgroundColor: '#f5f5f5',
        }}
      />
    );
  }

  // 计算已选择的对象数量
  const count = value?.length || 0;
  // 显示文本：有选择时显示数量，无选择时显示"请选择"
  const displayText = count > 0 ? count.toString() : '请选择';

  // 计算背景色 - disabled 不影响样式
  const getBackgroundColor = () => {
    if (type === 'view') return '#f9f9f9';
    return '#fff';
  };

  return (
    <>
      {/* 输入框样式的选择器 */}
      <Input
        value={displayText}
        readOnly // 只读，不允许直接输入
        placeholder="请选择"
        style={{
          cursor: 'pointer', // disabled 不影响样式，始终显示为可点击
          backgroundColor: getBackgroundColor(),
        }}
        onClick={openModal}
      />

      {/* 溯源对象选择穿梭框 */}
      <ConfigProvider locale={zhCN}>
        <Modal
          title={type === 'view' ? '查看溯源对象' : '选择溯源对象'}
          visible={transferVisible}
          onOk={type === 'view' ? undefined : handleTransferOk}
          onCancel={handleTransferCancel}
          okText={type === 'view' ? undefined : '确定'}
          width={1200}
          cancelText={type === 'view' ? '关闭' : '取消'}
          confirmLoading={loading} // 使用loading状态控制确认按钮的加载状态
          footer={
            type === 'view'
              ? [
                  <Button key="close" onClick={handleTransferCancel}>
                    关闭
                  </Button>,
                ]
              : undefined
          }
        >
          <div className={styles.traceObjectSelector}>
            <Transfer
              dataSource={traceData}
              targetKeys={transferTargetKeys}
              onChange={type === 'view' ? undefined : handleTransferChange}
              titles={['可选对象', '已选对象']}
              showSearch
              showSelectAll={type !== 'view'}
              oneWay={false}
              disabled={type === 'view'}
              listStyle={{
                width: 520,
                height: 500,
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
              }}
              className={styles.transferContainer}
              locale={{
                itemUnit: '项',
                itemsUnit: '项',
                searchPlaceholder: '搜索公司名称或指标',
                notFoundContent: '暂无数据',
              }}
              filterOption={(inputValue, option) => {
                // 自定义搜索逻辑，搜索公司名称和溯源指标
                const searchText = inputValue.toLowerCase();
                const company = (option.company || '').toLowerCase();
                const traceIndex = (option.traceIndex || '').toLowerCase();
                const title = (option.title || '').toLowerCase();
                return (
                  company.includes(searchText) ||
                  traceIndex.includes(searchText) ||
                  title.includes(searchText)
                );
              }}
            >
              {renderTransferList}
            </Transfer>
          </div>
        </Modal>
      </ConfigProvider>
    </>
  );
};

export default TraceObjectSelector;
